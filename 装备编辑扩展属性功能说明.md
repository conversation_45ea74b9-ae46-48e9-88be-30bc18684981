# 装备编辑扩展属性功能说明

## 概述
为玉帝后台版的装备编辑功能添加了18个特殊属性支持，包括掉落率、经验获取、幸运值等游戏核心属性。现在可以通过装备编辑功能直接设置这些特殊属性。

## 修改的文件

### 1. 右键菜单装备编辑功能
**文件**: `scripts玉帝后台版/UI/base/btnList/BtnList.as`

### 2. 装备重铸界面编辑功能
**文件**: `scripts玉帝后台版/UI/forging/equipRemake/EquipRemakeBoard.as`

## 支持的属性列表

### 基础掉落属性 (12-18)
| 指令代码 | 中文名称 | 属性名 | 说明 |
|---------|---------|--------|------|
| 12 | 无双水晶掉落 | demStroneDropNum | 设置无双水晶掉落数量 |
| 13 | 万能球掉落 | demBallDropNum | 设置万能球掉落数量 |
| 14 | 战神之心掉落 | madheartDropNum | 设置战神之心掉落数量 |
| 15 | 幸运值 | lottery | 设置幸运值 |
| 16 | 商运掉率 | coinMul | 设置银币获取倍率(%) |
| 17 | 优胜券获取 | arenaStampDropNum | 设置优胜券获取数量 |
| 18 | 载具碎片掉落 | vehicleCashDropNum | 设置载具碎片掉落数量 |

### 高级掉率属性 (19-29)
| 指令代码 | 中文名称 | 属性名 | 说明 |
|---------|---------|--------|------|
| 19 | 生命催化剂掉率 | lifeCatalystDropPro | 设置生命催化剂掉率(%) |
| 20 | 神能石掉率 | godStoneDropPro | 设置神能石掉率(%) |
| 21 | 转化石掉率 | converStoneDropPro | 设置转化石掉率(%) |
| 22 | 化石掉率 | taxStampDropPro | 设置化石掉率(%) |
| 23 | 血手掉率 | bloodStoneDropPro | 设置血手掉率(%) |
| 24 | 装置掉率 | deviceDropPro | 设置装置掉率(%) |
| 25 | 赠礼好感度 | loveAdd | 设置赠礼好感度点数 |
| 26 | 好感度每天 | dayLoveAdd | 设置每日好感度点数 |
| 27 | 战斗力神级 | dpsAll | 设置战斗力/神级(%) |
| 28 | 防弹值 | bulletDedut | 设置防弹值(%) |
| 29 | 每日扫荡次数 | sweepingNum | 设置每日扫荡次数 |

## 使用方法

### 1. 右键菜单编辑
1. 在背包或装备界面右键点击装备
2. 选择"装备编辑"选项
3. 在弹出的输入框中输入编辑指令
4. 支持多指令组合，用&连接

### 2. 装备重铸界面编辑
1. 进入锻造界面 → 装备 → 装备重铸
2. 选择要编辑的装备
3. 点击编辑按钮
4. 在弹出的输入框中输入编辑指令

## 指令格式

### 基本格式
```
指令代码*数值
```

### 中文指令格式
```
中文属性名*数值
```

### 多指令组合
```
指令1*值1&指令2*值2&指令3*值3
```

## 使用示例

### 基础属性设置
```
12*999          # 设置无双水晶掉落999个
15*999          # 设置幸运值999
16*99900        # 设置商运掉率99900%
29*999          # 设置每日扫荡次数999次
```

### 掉率属性设置
```
19*99900        # 设置生命催化剂掉率99900%
20*99900        # 设置神能石掉率99900%
37*99900        # 设置宝石掉率99900%
```

### 经验和好感度设置
```
30*999          # 设置经验获取999%
25*999          # 设置赠礼好感度999点
26*999          # 设置每日好感度999点
```

### 战斗属性设置
```
27*15           # 设置战斗力/神级15%
28*39           # 设置防弹值39%
```

### 组合指令示例
```
12*999&13*999&14*999&15*999&16*99900&17*999&18*999
```
这个指令会同时设置：
- 无双水晶掉落999个
- 万能球掉落999个  
- 战神之心掉落999个
- 幸运值999
- 商运掉率99900%
- 优胜券获取999个
- 载具碎片掉落999个

### 中文指令示例
```
无双水晶掉落*999&幸运值*999&战斗力神级*15&防弹值*39
```

## 注意事项

### 1. 数值转换
- **掉率属性**: 输入的百分比数值会自动转换为倍率（除以100）
- **数量属性**: 直接使用输入的数值
- **例如**: 输入`16*99900`会设置coinMul为999（99900/100）

### 2. 属性存储
- 所有扩展属性都存储在装备的`obj`对象中
- 如果装备原本没有obj对象，系统会自动创建

### 3. 兼容性
- 新功能与原有的装备编辑功能完全兼容
- 可以同时使用基础属性编辑和扩展属性编辑
- 支持与技能编辑等其他功能组合使用

### 4. 成功提示
- 每个属性设置成功后都会显示确认消息
- 消息中会显示设置的具体数值和单位

## 完整指令参考

### 原有基础指令 (00-11)
```
00*数值 或 等级*数值        # 设置装备等级
01*颜色 或 颜色*颜色        # 设置装备品质
02*数值 或 强化*数值        # 设置强化等级
03*数值 或 进化*数值        # 设置进化等级
04*类型 或 类型*类型        # 设置装备类型
05*数值 或 基础等级*数值     # 设置基础等级
06*时间 或 获取时间*时间     # 设置获取时间
07*时间 或 到期时间*时间     # 设置到期时间
08 或 永不过期             # 设置永不过期
09*技能 或 技能添加*技能     # 添加技能
10*技能 或 技能删除*技能     # 删除技能
11 或 copyData            # 清除复制数据
```

### 支持的扩展指令 (12-29)
```
12*数值 或 无双水晶掉落*数值
13*数值 或 万能球掉落*数值
14*数值 或 战神之心掉落*数值
15*数值 或 幸运值*数值
16*数值 或 商运掉率*数值
17*数值 或 优胜券获取*数值
18*数值 或 载具碎片掉落*数值
19*数值 或 生命催化剂掉率*数值
20*数值 或 神能石掉率*数值
21*数值 或 转化石掉率*数值
22*数值 或 化石掉率*数值
23*数值 或 血手掉率*数值
24*数值 或 装置掉率*数值
25*数值 或 赠礼好感度*数值
26*数值 或 好感度每天*数值
27*数值 或 战斗力神级*数值
28*数值 或 防弹值*数值
29*数值 或 每日扫荡次数*数值
```

## 实际应用场景

### 1. 打造您需要的全能神装
```
12*999&13*999&14*999&15*999&16*99900&17*999&18*999&19*99900&20*99900&21*99900&22*99900&23*99900&24*99900&25*999&26*999&27*15&28*39&29*999
```

这个指令会同时设置：
- 无双水晶掉落 +999个
- 万能球掉落 +999个
- 战神之心掉落 +999个
- 幸运值 999
- 商运掉率 +99900%
- 优胜券获取 +999
- 载具碎片掉落 +999
- 生命催化剂掉率+99900%
- 神能石掉率 +99900%
- 转化石掉率 +99900%
- 化石掉率 +99900%
- 血手掉率 +99900%
- 装置掉率 +99900%
- 赠礼好感度 +999点
- 好感度 每天+999点
- 战斗力/神级 15%
- 防弹值 39%
- 每日扫荡次数 +999次

### 2. 分类测试指令

#### 掉落物品类
```
12*999&13*999&14*999&17*999&18*999
```

#### 掉率提升类
```
16*99900&19*99900&20*99900&21*99900&22*99900&23*99900&24*99900
```

#### 好感度类
```
25*999&26*999
```

#### 战斗属性类
```
15*999&27*15&28*39&29*999
```

## 快速测试指令

### 测试单个属性
```
15*999          # 测试幸运值设置
```

### 测试掉落率属性
```
19*99900        # 测试生命催化剂掉率99900%
```

### 测试中文指令
```
幸运值*999&战斗力神级*15&无双水晶掉落*999
```

## 功能验证方法

1. **选择一件装备**进行编辑测试
2. **使用简单指令**先测试基础功能
3. **查看装备属性**确认设置是否生效
4. **逐步测试复杂指令**确保所有功能正常

## 故障排除

### 常见问题
1. **指令无效**: 检查指令格式是否正确，确保使用*分隔
2. **属性未生效**: 确认装备已保存，重新查看装备属性
3. **数值异常**: 检查输入的数值是否在合理范围内

### 调试建议
1. 先测试原有的基础指令（00-11）确保编辑功能正常
2. 再测试新增的扩展指令（12-29）
3. 使用单个指令测试，确认每个属性都能正常设置
4. 最后测试组合指令

## 更新日志

### v2.0 (当前版本)
- 精简为18个核心扩展属性支持
- 删除不常用的属性，专注核心功能
- 支持中文和数字指令
- 支持百分比自动转换
- 完善错误提示和成功反馈
- 兼容原有装备编辑功能
