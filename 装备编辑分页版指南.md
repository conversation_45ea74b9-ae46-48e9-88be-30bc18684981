# 装备编辑分页版指南

## 🎯 功能说明

装备编辑功能现在采用分页设计，将原来冗长的单页面拆分为3个清晰的页面，让界面更美观易用。

## 📋 分页结构

### 主菜单
```
装备编辑菜单

【1】基础属性编辑
【2】技能管理编辑  
【3】时间设置编辑

请输入页面编号(1-3)或直接输入指令：
```

## 📄 页面详情

### 页面1：基础属性编辑
```
基础属性编辑

00*数值 或 等级*数值      # 设置装备等级
01*颜色 或 颜色*颜色      # 设置装备品质
02*数值 或 强化*数值      # 设置强化等级
03*数值 或 进化*数值      # 设置进化等级
04*类型 或 类型*类型      # 设置装备类型
05*数值 或 基础等级*数值   # 设置基础等级

颜色: 白绿蓝紫橙红黑暗金紫金氩星
类型: 衣服裤子头盔腰带时装

示例: 00*99&01*红&02*20&03*5

请输入编辑指令：
```

### 页面2：技能管理编辑
```
技能管理编辑

09*技能 或 技能添加*技能   # 添加单个技能
09*全部 或 技能添加*全部   # 添加所有技能
10*技能 或 技能删除*技能   # 删除技能

常用技能代码:
头盔/腰带: godHand_equip immune_equip
战衣/裤子: sacrifice_equip backStrong_equip
时装: summonWolf_bigBoss zoomOut

示例: 09*全部 或 09*godHand_equip

请输入编辑指令：
```

### 页面3：时间设置编辑
```
时间设置编辑

06*时间 或 获取时间*时间   # 设置获取时间
07*时间 或 到期时间*时间   # 设置到期时间
08 或 永不过期           # 设置永不过期
11 或 copyData          # 清除复制数据

时间格式: 2025-05-20 12:00:00

示例: 08 或 07*2025-12-31 23:59:59

请输入编辑指令：
```

## 🎮 使用方法

### 方法1：右键装备编辑（分页版）
1. 在背包中右键点击装备
2. 选择"装备编辑"
3. 看到主菜单，输入页面编号(1-3)
4. 进入对应页面，输入具体指令
5. 确认执行

### 方法2：装备重铸界面（分页版）
1. 进入锻造界面 → 装备 → 装备重铸
2. 选择要编辑的装备
3. 点击"编辑当前数据"
4. 看到主菜单，输入页面编号(1-3)
5. 进入对应页面，输入具体指令

### 方法3：直接输入指令
在主菜单或任何页面都可以直接输入完整指令，无需分页：
```
00*99&01*氩星&02*99&03*9&08&09*全部
```

## 🔧 分页优势

### ✅ 界面美观
- 每页内容精简，不再拥挤
- 分类清晰，功能明确
- 排版整齐，易于阅读

### ✅ 操作便捷
- 按功能分类，快速定位
- 支持页面导航和直接指令
- 保持向后兼容

### ✅ 学习友好
- 新手可按页面学习
- 高手可直接输入指令
- 每页都有示例说明

## 📊 分页对比

### 修复前（单页面）
```
装备编辑

00*数值 或 等级*数值      # 设置装备等级
01*颜色 或 颜色*颜色      # 设置装备品质
02*数值 或 强化*数值      # 设置强化等级
03*数值 或 进化*数值      # 设置进化等级
04*类型 或 类型*类型      # 设置装备类型
05*数值 或 基础等级*数值   # 设置基础等级
06*时间 或 获取时间*时间   # 设置获取时间
07*时间 或 到期时间*时间   # 设置到期时间
08 或 永不过期           # 设置永不过期
09*技能 或 技能添加*技能   # 添加技能
09*全部 或 技能添加*全部   # 添加所有技能
10*技能 或 技能删除*技能   # 删除技能
11 或 copyData          # 清除复制数据

请输入编辑指令：
```
**问题**: 内容太多，界面拥挤，难以阅读

### 修复后（分页版）
```
装备编辑菜单

【1】基础属性编辑
【2】技能管理编辑
【3】时间设置编辑

请输入页面编号(1-3)或直接输入指令：
```
**优势**: 简洁明了，分类清晰，美观易用

## 🧪 测试指南

### 分页导航测试
1. 右键装备 → 装备编辑
2. 输入 `1` → 进入基础属性页面
3. 输入 `2` → 进入技能管理页面  
4. 输入 `3` → 进入时间设置页面

### 直接指令测试
在主菜单直接输入：
```
00*99&01*红&02*20
```
**预期结果**: 直接执行指令，无需进入子页面

### 页面功能测试
在页面2输入：
```
09*全部
```
**预期结果**: 添加所有对应类型的技能

## 🚀 立即体验

现在装备编辑界面更加美观易用：

**快速测试**:
1. 右键装备 → 装备编辑
2. 输入 `2` 进入技能页面
3. 输入 `09*全部` 添加所有技能

**完整测试**:
1. 右键装备 → 装备编辑  
2. 直接输入 `00*99&01*氩星&02*99&03*9&08&09*全部`

## 🔍 技术实现

### 修改的文件
1. `scripts玉帝后台版/UI/bag/ItemsGripBtnListCtrl.as` - 右键菜单
2. `scripts玉帝后台版/UI/forging/equipRemake/EquipRemakeBoard.as` - 装备重铸界面

### 关键改进
- 统一了两个界面的分页结构
- 保持了直接指令输入的兼容性
- 优化了界面布局和用户体验

装备编辑功能现在既美观又实用，解决了界面拥挤的问题！
