package UI.bag
{
   import UI.UIShow;
   import UI.base.CheckText;
   import UI.base.btnList.BtnList;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.grid.NormalGrid;
   import UI.test.SaveTestBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.parts.PartsCreator;
   import dataAll._app.parts.PartsDataGroup;
   import dataAll._app.parts.PartsMethod;
   import dataAll._app.parts.define.PartsType;
   import dataAll._player.more.MoreData;
   import dataAll._player.more.MoreDataGroup;
   import dataAll.arms.ArmsData;
   import dataAll.arms.ArmsDataGroup;
   import dataAll.arms.creator.ArmsPiano;
   import dataAll.arms.skin.IO_HaveSkinData;
   import dataAll.equip.EquipData;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.EquipDataSwapPan;
   import dataAll.equip.creator.BlackEquipComposeCtrl;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.device.DeviceData;
   import dataAll.equip.suit.SuitCtreator;
   import dataAll.equip.vehicle.VehicleData;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.creator.ItemsComposeCtrl;
   import dataAll.items.creator.ItemsRefiningCtrl;
   import dataAll.pet.gene.GeneData;
   import dataAll.pet.gene.GeneDataGroup;
   import dataAll.pet.gene.creator.GeneDataDecompose;
   import dataAll.skill.HeroSkillData;
   import dataAll.skill.HeroSkillDataGroup;
   import dataAll.skill.define.HeroSkillDefine;
   import dataAll.things.ThingsData;
   import dataAll.things.ThingsDataGroup;
   import dataAll.things.ThingsUseCtrl;
   import dataAll.things.define.ThingsDefine;
   import dataAll.ui.tip.CheckData;
   import flash.events.Event;
   import flash.system.System;
   
   public class ItemsGripBtnListCtrl
   {
      
      private static var nowDa:IO_ItemsData;
      
      private static var nowDg:ItemsDataGroup;
      
      private static var nowGrip:NormalGrid;
      
      private static var btnList:BtnList = null;
      
      private static var nowItemsName:String = "";
      
      private static var nowItemsType:String = "";
      
      private static var imYesFun:Function = null;
      
      public static var armsData:ArmsData = null;
      
      public static var equipData:EquipData = null;
      
      private static var imCn:String = "";
      
      public function ItemsGripBtnListCtrl()
      {
         super();
      }
      
      public static function init() : void
      {
         btnList = Gaming.uiGroup.btnList;
         btnList.addEventListener(ClickEvent.ON_CLICK,btnClick);
         btnList.addEventListener(ClickEvent.ON_DOUBLE_CLICK,btnClick);
      }
      
      public static function show(grip0:NormalGrid, fatherData0:ItemsDataGroup) : void
      {
         var da0:Object = null;
         var thingsDa0:ThingsData = null;
         var listArr0:Array = [];
         nowGrip = grip0;
         if(grip0.canShowTipB())
         {
            da0 = grip0.itemsData;
            if(da0 is ArmsData)
            {
               ItemsGripBtnListCtrl.armsData = da0 as ArmsData;
               listArr0 = armsTip(da0 as ArmsData,fatherData0 as ArmsDataGroup);
            }
            else if(da0 is EquipData)
            {
               ItemsGripBtnListCtrl.equipData = da0 as EquipData;
               listArr0 = equipTip(da0 as EquipData,fatherData0 as EquipDataGroup);
            }
            else if(fatherData0 is PartsDataGroup)
            {
               listArr0 = partsTip(da0 as ThingsData,fatherData0 as PartsDataGroup);
            }
            else if(fatherData0 is ThingsDataGroup)
            {
               listArr0 = thingsTip(da0 as ThingsData,fatherData0 as ThingsDataGroup);
            }
            else if(da0 is HeroSkillData)
            {
               listArr0 = skillTip(da0 as HeroSkillData,fatherData0 as HeroSkillDataGroup);
            }
            else if(da0 is MoreData)
            {
               listArr0 = moreTip(da0 as MoreData,fatherData0 as MoreDataGroup);
            }
            else if(da0 is GeneData)
            {
               listArr0 = geneTip(da0 as GeneData,fatherData0 as GeneDataGroup);
            }
            dealAllListArr(da0 as IO_ItemsData,fatherData0 as ItemsDataGroup,listArr0);
            dealGiftListArr(grip0,listArr0);
            nowDa = da0 as IO_ItemsData;
            nowDg = fatherData0 as ItemsDataGroup;
            if(Boolean(nowDa))
            {
               if(Gaming.testCtrl.cheating.enabled)
               {
                  thingsDa0 = nowDa as ThingsData;
                  SaveTestBox.addText(nowDa.getSave().name + (Boolean(thingsDa0) ? "   " + thingsDa0.save.getDefine().smeltD.price : ""));
               }
            }
         }
         if(listArr0.length > 0)
         {
            listArr0 = BtnList.sortTextArr(listArr0);
            btnList.show(listArr0,nowDa);
            btnList.setPositionBySp(grip0);
         }
         else
         {
            hide();
         }
      }
      
      public static function hide() : void
      {
         btnList.hide();
      }
      
      public static function clearData() : void
      {
         nowDg = null;
         nowDa = null;
      }
      
      public static function importantCheck(da0:IO_ItemsData, cn0:String, yesFun0:Function) : void
      {
         if(da0.getSave().isImportantB())
         {
            imCn = cn0;
            imYesFun = yesFun0;
            Gaming.uiGroup.alertBox.textInput.showTextInput("此为重要物品，请输入“" + cn0 + "”，才能继续。","",yes_importantCheck);
         }
         else
         {
            yesFun0();
         }
      }
      
      private static function yes_importantCheck(str0:String) : void
      {
         if(str0 == imCn)
         {
            imYesFun();
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("输入错误！");
         }
      }
      
      public static function addGiftListEvent(box0:ItemsGripBox) : void
      {
         box0.addEventListener(ClickEvent.ON_CLICK,giftClick);
      }
      
      private static function giftClick(e:ClickEvent) : void
      {
         showGift(e.child as NormalGrid);
         Gaming.uiGroup.tipBox.hide();
      }
      
      private static function showGift(grip0:NormalGrid) : void
      {
         var listArr0:Array = null;
         nowDa = null;
         nowDg = null;
         nowGrip = grip0;
         if(grip0.canShowTipB())
         {
            listArr0 = [];
            dealGiftListArr(grip0,listArr0);
            if(listArr0.length > 0)
            {
               listArr0 = BtnList.sortTextArr(listArr0);
               btnList.show(listArr0,nowDa);
               btnList.setPositionBySp(grip0);
            }
            else
            {
               hide();
            }
         }
      }
      
      private static function dealGiftListArr(grip0:NormalGrid, arr0:Array) : void
      {
         var fatherArr0:Array = null;
         var gdarr0:Array = null;
         var gd0:GoodsDefine = null;
         var itemsName0:String = grip0.getShopItemsName();
         nowItemsName = itemsName0;
         nowItemsType = grip0.getShopItemsType();
         if(itemsName0 != "")
         {
            fatherArr0 = ["normal","score","anniCoin"];
            gdarr0 = Gaming.defineGroup.goods.getDefineArrByDefineLabel(itemsName0);
            for each(gd0 in gdarr0)
            {
               if(fatherArr0.indexOf(gd0.father) >= 0)
               {
                  arr0.push(gd0.father + "Shop");
               }
            }
         }
      }
      
      private static function shopBtnClick(label0:String) : void
      {
         var gd0:GoodsDefine = null;
         var father0:String = label0.replace("Shop","");
         var itemsName0:String = nowItemsName;
         var gdarr0:Array = Gaming.defineGroup.goods.getDefineArrByDefineLabel(itemsName0);
         for each(gd0 in gdarr0)
         {
            if(gd0.father == father0)
            {
               Gaming.uiGroup.shopUI.buyDefineName(gd0.name);
               return;
            }
         }
      }
      
      public static function fleshAllBy(dg1:ItemsDataGroup, bagMustShowLabelB0:Boolean = true) : void
      {
         var fleshBagB:Boolean = false;
         var fleshSkillB:Boolean = false;
         var fleshPartsB:Boolean = false;
         if(dg1.dataType == ItemsDataGroup.TYPE_EQUIP)
         {
            fleshBagB = true;
         }
         else if(dg1.dataType == ItemsDataGroup.TYPE_ARMS)
         {
            fleshBagB = true;
         }
         else if(dg1.dataType == ItemsDataGroup.TYPE_THINGS)
         {
            fleshBagB = true;
         }
         else if(dg1.dataType == ItemsDataGroup.TYPE_GENE)
         {
            fleshBagB = true;
         }
         else if(dg1.dataType == ItemsDataGroup.TYPE_PARTS)
         {
            fleshBagB = true;
            fleshPartsB = true;
         }
         else if(dg1.dataType == ItemsDataGroup.TYPE_SKILL)
         {
            fleshSkillB = true;
         }
         if(fleshBagB)
         {
            Gaming.PG.changeEquip();
            Gaming.PG.changeArms();
            if(bagMustShowLabelB0)
            {
               Gaming.uiGroup.bagUI.showBox(dg1.dataType);
            }
            else
            {
               Gaming.uiGroup.bagUI.fleshAllBox();
            }
            Gaming.uiGroup.wearUI.fleshAllBox();
            Gaming.uiGroup.houseUI.fleshData();
         }
         if(fleshSkillB)
         {
            Gaming.uiGroup.skillUI.wearBox.fleshData();
            Gaming.uiGroup.skillUI.upgradeBox.fleshData();
         }
         if(fleshPartsB)
         {
            Gaming.uiGroup.partsUI.fleshNowData();
         }
      }
      
      private static function fleshNowGrip() : void
      {
         var grip0:ItemsGrid = nowGrip as ItemsGrid;
         if(Boolean(grip0) && Boolean(nowDa))
         {
            grip0.inDataByAllItems(nowDa);
         }
      }
      
      private static function btnClick(e:ClickEvent) : void
      {
         var btn0:NormalBtn = e.child as NormalBtn;
         if(Boolean(nowDg))
         {
            ItemsGripBtnListCtrl[nowDg.dataType + "Click"](btn0.label);
         }
         deallAllClick(btn0.label);
         hide();
      }
      
      public static function mouseOverGripEvent(e:Event) : void
      {
         var grip0:ItemsGrid = e.target as ItemsGrid;
         if(e is ClickEvent)
         {
            grip0 = (e as ClickEvent).child as ItemsGrid;
         }
         if(grip0 is ItemsGrid)
         {
         }
         if(nowDa is MoreData)
         {
            hide();
         }
      }
      
      private static function dealAllListArr(da0:IO_ItemsData, dg0:ItemsDataGroup, listArr0:Array) : void
      {
         var onlyWearB0:Boolean = false;
         if(dg0.placeType == ItemsDataGroup.PLACE_BAG)
         {
         }
         if(da0.canResolveB())
         {
            onlyWearB0 = da0 is ArmsData && dg0.placeType == ItemsDataGroup.PLACE_WEAR && dg0.dataArr.length <= 1;
            if(onlyWearB0 == false)
            {
               listArr0.unshift("resolve");
            }
         }
      }
      
      private static function deallAllClick(label0:String) : void
      {
         if(label0 != "trading")
         {
            if(label0 == "resolve")
            {
               ItemsResolveCtrl.showResolve(nowDa,nowDg);
            }
            else if(label0.indexOf("Shop") > 0)
            {
               shopBtnClick(label0);
            }
         }
      }
      
      private static function armsTip(da0:ArmsData, dg0:ArmsDataGroup) : Array
      {
         var listArr0:Array = [];
         var wearShowB0:Boolean = Gaming.uiGroup.wearUI.visible;
         var houseShowB0:Boolean = Gaming.uiGroup.houseUI.visible;
         var canB:Boolean = PartsMethod.canB();
         var levelCanB:Boolean = PartsMethod.levelCanB();
         var isGamingB:Boolean = Gaming.LG.isGaming();
         var lockStr0:String = da0.save.lockB ? "unlock" : "lock";
         var onlyWearB0:Boolean = dg0.placeType == ItemsDataGroup.PLACE_WEAR && dg0.dataArr.length <= 1;
         if(dg0.placeType == ItemsDataGroup.PLACE_BAG)
         {
            listArr0 = [lockStr0];
            if(wearShowB0)
            {
               listArr0.unshift("load");
            }
            else if(houseShowB0)
            {
               listArr0.unshift("inHouse");
            }
            if(canB)
            {
               listArr0.push("assembly");
            }
         }
         else if(dg0.placeType == ItemsDataGroup.PLACE_HOUSE)
         {
            listArr0 = ["outHouse",lockStr0];
         }
         else
         {
            if(onlyWearB0)
            {
               if(canB)
               {
                  listArr0 = ["assembly",lockStr0];
               }
            }
            else
            {
               listArr0 = [lockStr0];
               if(wearShowB0)
               {
                  listArr0.unshift("unload");
               }
               if(canB)
               {
                  listArr0.push("assembly");
               }
            }
            listArr0.push(da0.save.firstChoiceB ? "noFirstChoice" : "firstChoice");
         }
         if(onlyWearB0 == false && da0.save.isOnceGetB() == false)
         {
            listArr0.push("sell");
            if(levelCanB && dg0.placeType != ItemsDataGroup.PLACE_HOUSE)
            {
               listArr0.push("decompose");
            }
         }
         if(dg0.placeType != ItemsDataGroup.PLACE_HOUSE && !isGamingB)
         {
            listArr0.push("upgrade");
            listArr0.push("remake");
            listArr0.push("element");
            if(da0.canStrengthenB())
            {
               listArr0.push("strengthen");
            }
            if(da0.isCanEvoB())
            {
               listArr0.push("evo");
            }
            if(da0.canRefiningB() && !onlyWearB0)
            {
               listArr0.push("refining");
            }
            if(da0.canEditPathB())
            {
               listArr0.push("editPath");
            }
            if(da0.isPianoB())
            {
               listArr0.push("editPiano");
            }
            if(da0.haveMoreSkinB())
            {
               listArr0.push("skin");
            }
         }
         // 添加武器编辑按钮
         if(Gaming.testCtrl.cheating.enabled)
         {
            listArr0.push("armsEdit");
         }
         return listArr0;
      }
      
      private static function armsClick(label0:String) : void
      {
         var site2:int = 0;
         var type_da0:ArmsData = null;
         var da0:ArmsData = nowDa as ArmsData;
         var dg0:ArmsDataGroup = nowDg as ArmsDataGroup;
         var dg2:ArmsDataGroup = Gaming.PG.getOpposingDataGroup(dg0) as ArmsDataGroup;
         if(label0 == "sell")
         {
            if(da0.save.lockB)
            {
               Gaming.uiGroup.alertBox.showError("已锁定武器不能被出售。");
            }
            else
            {
               armsSell(da0,dg0);
            }
         }
         else if(label0 == "load")
         {
            site2 = dg2.spaceSiteOf();
            if(site2 == -1)
            {
               type_da0 = dg2.getDataByArmsType(da0.save.getArmsType());
               if(type_da0 is ArmsData)
               {
                  site2 = type_da0.save.site;
               }
               else
               {
                  site2 = 0;
               }
            }
            ItemsGripMoveCtrl.swap(dg0,dg2,da0.getSave().site,site2);
            fleshAllBy(dg0);
         }
         else if(label0 == "unload")
         {
            unload(da0,dg0);
         }
         else if(label0 == "outHouse")
         {
            unloadTo(da0,dg0,Gaming.PG.da.armsBag);
         }
         else if(label0 == "inHouse")
         {
            unloadTo(da0,dg0,Gaming.PG.da.armsHouse);
         }
         else if(label0 == "unlock")
         {
            unlock(da0,dg0);
         }
         else if(label0 == "lock")
         {
            lock(da0,dg0);
         }
         else if(label0 == "decompose")
         {
            decomposeItemsData(da0,dg0);
         }
         else if(label0 == "assembly")
         {
            assemblyArms(da0,dg0);
         }
         else if(label0 == "upgrade")
         {
            Gaming.uiGroup.forgingUI.gotoByOneData(da0,"armsUpgrade");
         }
         else if(label0 == "remake")
         {
            Gaming.uiGroup.forgingUI.gotoByOneData(da0,"armsRemake");
         }
         else if(label0 == "element")
         {
            Gaming.uiGroup.forgingUI.gotoByOneData(da0,"armsEle");
         }
         else if(label0 == "strengthen")
         {
            Gaming.uiGroup.forgingUI.gotoByOneData(da0,"armsStrengthen");
         }
         else if(label0 == "evo")
         {
            Gaming.uiGroup.forgingUI.gotoByOneData(da0,"armsEvo");
         }
         else if(label0 == "refining")
         {
            ItemsRefiningCtrl.refining(da0,dg0);
         }
         else if(label0 == "editPath")
         {
            Gaming.uiGroup.bulletPathBox.editData(da0);
         }
         else if(label0 == "editPiano")
         {
            ArmsPiano.editData(da0);
         }
         else if(label0 == "firstChoice")
         {
            da0.save.firstChoiceB = true;
            fleshAllBy(dg0,false);
         }
         else if(label0 == "noFirstChoice")
         {
            da0.save.firstChoiceB = false;
            fleshAllBy(dg0,false);
         }
         else if(label0 == "skin")
         {
            Gaming.uiGroup.armsSkinBox.showData(da0,nowGrip);
         }
         else if(label0 == "armsEdit")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("武器编辑","请输入编辑指令",function(str:String):void{
               Gaming.uiGroup.btnList.ArmsEdit(str);
            },"yesAndNo",999);
         }
      }
      
      private static function armsSell(da0:ArmsData, dg0:ArmsDataGroup) : void
      {
         nowDa = da0;
         nowDg = dg0;
         var price0:Number = da0.getSellPrice();
         var converNum0:int = da0.getAllUpgradeConverStoneNum();
         var str0:String = "你确定要出售" + da0.getCnType() + " " + ComMethod.color(da0.getSave().cnName,"#00FF00") + (da0.havePartsB() ? "（内含零件）" : "") + " ？\n出售后可获得：";
         str0 += "\n银币：" + ComMethod.color(price0 + "","#FFFF00");
         if(converNum0 > 0)
         {
            str0 += "    转化石：" + ComMethod.color(converNum0 + "","#FFFF00");
         }
         Gaming.uiGroup.alertBox.showCheck(str0,"yesAndNo",0,parts_armsSellPan);
      }
      
      private static function parts_armsSellPan() : void
      {
         importantCheck(nowDa,"出售",parts_armsSell);
      }
      
      private static function parts_armsSell() : void
      {
         var saveArr0:Array = null;
         var bagStr0:String = null;
         var da0:ArmsData = nowDa as ArmsData;
         var dg0:ArmsDataGroup = nowDg as ArmsDataGroup;
         if(da0.havePartsB())
         {
            saveArr0 = da0.partsData.saveGroup.arr;
            bagStr0 = Gaming.PG.da.partsBag.bagSpacePanBySaveArr(saveArr0);
            if(bagStr0 != "")
            {
               bagStr0 = "无法出售武器。\n" + bagStr0;
               Gaming.uiGroup.alertBox.showNormal(bagStr0,"yes",null,null,"no");
            }
            else
            {
               yes_armsSell();
            }
         }
         else
         {
            yes_armsSell();
         }
      }
      
      private static function yes_armsSell() : void
      {
         var saveArr0:Array = null;
         var da0:ArmsData = nowDa as ArmsData;
         var dg0:ArmsDataGroup = nowDg as ArmsDataGroup;
         if(da0.havePartsB())
         {
            saveArr0 = da0.partsData.saveGroup.arr;
            Gaming.PG.da.partsBag.addBySaveArr(saveArr0);
         }
         var price0:Number = da0.getSellPrice();
         dg0.removeData(da0);
         var converNum0:int = da0.getAllUpgradeConverStoneNum();
         if(converNum0 > 0)
         {
            Gaming.PG.da.thingsBag.addDataByName("converStone",converNum0);
         }
         Gaming.PG.da.main.addCoin(price0);
         flesh_affter_sell(da0,dg0);
      }
      
      private static function assemblyArms(da0:ArmsData, dg0:ArmsDataGroup) : void
      {
         UIShow.showApp("parts",true);
         Gaming.uiGroup.partsUI.showBox("assembly");
         Gaming.uiGroup.partsUI.assemblyBox.chooseArmsData(da0);
         Gaming.uiGroup.bagUI.showAndLabel("parts");
      }
      
      private static function equipTip(da0:EquipData, dg0:EquipDataGroup) : Array
      {
         var check0:CheckData = null;
         var fashionBtnName0:String = null;
         var vehicleDa0:VehicleData = null;
         var skillD0:HeroSkillDefine = null;
         var listArr0:Array = [];
         var wearShowB0:Boolean = Gaming.uiGroup.wearUI.visible;
         var levelCanB:Boolean = PartsMethod.levelCanB() && EquipType.NORMAL_ARR.indexOf(da0.save.partType) >= 0;
         var houseShowB0:Boolean = Gaming.uiGroup.houseUI.visible;
         var lockStr0:String = da0.save.lockB ? "unlock" : "lock";
         var isGamingB:Boolean = Gaming.LG.isGaming();
         if(dg0.placeType == ItemsDataGroup.PLACE_BAG)
         {
            listArr0 = [lockStr0];
            if(wearShowB0)
            {
               check0 = EquipDataSwapPan.canLoadPan(Gaming.PG.DATA,da0);
               if(check0.bb)
               {
                  listArr0.unshift("load");
               }
            }
            else if(houseShowB0)
            {
               listArr0.unshift("inHouse");
            }
         }
         else if(dg0.placeType == ItemsDataGroup.PLACE_HOUSE)
         {
            listArr0 = ["outHouse",lockStr0];
         }
         else
         {
            listArr0 = [lockStr0];
            if(wearShowB0)
            {
               listArr0.unshift("unload");
            }
         }
         if(da0.save.isOnceGetB() == false)
         {
            listArr0.push("sell");
         }
         if(!isGamingB)
         {
            fashionBtnName0 = da0.getFashionShowBtnName(Gaming.PG.DATA);
            if(fashionBtnName0 != "")
            {
               listArr0.unshift(fashionBtnName0);
            }
            if(da0.haveMoreSkinB())
            {
               listArr0.push("skin");
            }
         }
         if(da0.canLoadSuitB() && wearShowB0)
         {
            listArr0.splice(1,0,"loadSuit");
         }
         if(dg0.placeType != ItemsDataGroup.PLACE_HOUSE && !isGamingB)
         {
            if(da0.canUpgradeB())
            {
               listArr0.push("upgrade");
            }
            if(da0.canRemakeB())
            {
               listArr0.push("remake");
            }
            if(da0.canStrengthenB())
            {
               listArr0.push("strengthen");
            }
            if(levelCanB && da0.save.isOnceGetB() == false)
            {
               listArr0.unshift("decompose");
            }
            if(EquipType.advancedArr.indexOf(da0.save.partType) >= 0)
            {
               // 如果开启作弊模式，显示编辑按钮而不是进阶按钮
               if(Gaming.testCtrl.cheating.enabled)
               {
                  // 编辑按钮已在后面统一添加，这里不添加进阶按钮
               }
               else
               {
                  listArr0.push("advanced");
               }
            }
            if(da0.save.getDefine().isCanEvoB())
            {
               listArr0.push("evo");
            }
            if(da0.canRefiningB())
            {
               listArr0.push("refining");
            }
         }
         if(!isGamingB)
         {
            vehicleDa0 = da0 as VehicleData;
            if(Boolean(vehicleDa0))
            {
               if(vehicleDa0.vehicleDefine.getSkillArr().length > 0)
               {
                  listArr0.push("skill");
               }
            }
         }
         if(Gaming.testCtrl.cheating.enabled)
         {
            if(da0 is DeviceData)
            {
               skillD0 = Gaming.defineGroup.skill.getDefine(da0.save.skillArr[0]) as HeroSkillDefine;
               System.setClipboard(skillD0.getProjectText());
            }
         }
         // 添加装备编辑按钮
         if(Gaming.testCtrl.cheating.enabled)
         {
            if(da0 is WeaponData)
            {
               listArr0.push("weaponEdit");
            }
            else if(da0 is ShieldData)
            {
               listArr0.push("shieldEdit");
            }
            else if(da0 is DeviceData)
            {
               listArr0.push("deviceEdit");
            }
            else if(da0 is JewelryData)
            {
               listArr0.push("jewelryEdit");
            }
            else if(da0 is VehicleData)
            {
               listArr0.push("vehicleEdit");
            }
            else
            {
               listArr0.push("equipEdit");
            }
         }
         return listArr0;
      }
      
      private static function equipClick(label0:String) : void
      {
         var type0:String = null;
         var site2:int = 0;
         var da0:EquipData = nowDa as EquipData;
         var dg0:EquipDataGroup = nowDg as EquipDataGroup;
         var dg2:EquipDataGroup = Gaming.PG.getOpposingDataGroup(dg0) as EquipDataGroup;
         if(label0 == "sell")
         {
            if(da0.save.lockB)
            {
               Gaming.uiGroup.alertBox.showError("已锁定装备不能被出售。");
            }
            else
            {
               sell(da0,dg0);
            }
         }
         else if(label0 == "load")
         {
            type0 = da0.save.partType;
            site2 = EquipType.getSite(type0);
            ItemsGripMoveCtrl.swap(dg0,dg2,da0.getSave().site,site2);
            fleshAllBy(dg0);
            Gaming.uiGroup.wearUI.showHDFashionFastPan(da0);
         }
         else if(label0 == "loadSuit")
         {
            SuitCtreator.loadSuit(da0);
            fleshAllBy(dg0);
         }
         else if(label0 == "unload")
         {
            unload(da0,dg0);
         }
         else if(label0 == "useFashion")
         {
            if(dg0.placeType == ItemsDataGroup.PLACE_WEAR)
            {
               dg2.setRoleFashionShow(Gaming.PG.DATA.getRoleName(),null);
            }
            else
            {
               dg0.setRoleFashionShow(Gaming.PG.DATA.getRoleName(),da0);
            }
            fleshAllBy(dg0);
            Gaming.uiGroup.wearUI.showHDFashionFastPan(da0);
         }
         else if(label0 == "cancelFashion")
         {
            da0.setFashionShowRoleName("");
            fleshAllBy(dg0);
         }
         else if(label0 == "outHouse")
         {
            unloadTo(da0,dg0,Gaming.PG.da.equipBag);
         }
         else if(label0 == "inHouse")
         {
            unloadTo(da0,dg0,Gaming.PG.da.equipHouse);
         }
         else if(label0 == "unlock")
         {
            unlock(da0,dg0);
         }
         else if(label0 == "lock")
         {
            lock(da0,dg0);
         }
         else if(label0 == "decompose")
         {
            decomposeItemsData(da0,dg0);
         }
         else if(label0 == "upgrade")
         {
            if(da0 is VehicleData)
            {
               Gaming.uiGroup.vehicleUI.gotoUpgradeOne(da0 as VehicleData);
            }
            else
            {
               Gaming.uiGroup.forgingUI.gotoByOneData(da0,"equipUpgrade");
            }
         }
         else if(label0 == "advanced")
         {
            if(da0 is VehicleData)
            {
               Gaming.uiGroup.vehicleUI.gotoEvolutionOne(da0 as VehicleData);
            }
            else
            {
               Gaming.uiGroup.forgingUI.gotoByOneData(da0,(da0 as EquipData).save.partType + "Upgrade");
            }
         }
         else if(label0 == "evo")
         {
            Gaming.uiGroup.forgingUI.gotoByOneData(da0,"equipEvo");
         }
         else if(label0 == "refining")
         {
            ItemsRefiningCtrl.refining(da0,dg0);
         }
         else if(label0 == "remake")
         {
            Gaming.uiGroup.forgingUI.gotoByOneData(da0,"equipRemake");
         }
         else if(label0 == "strengthen")
         {
            if(da0 is VehicleData)
            {
               Gaming.uiGroup.vehicleUI.gotoStrengthenOne(da0 as VehicleData);
            }
            else
            {
               Gaming.uiGroup.forgingUI.gotoByOneData(da0,"equipStrengthen");
            }
         }
         else if(label0 == "skin")
         {
            Gaming.uiGroup.armsSkinBox.showData(da0 as IO_HaveSkinData,nowGrip);
         }
         else if(label0 == "skill")
         {
            if(da0 is VehicleData)
            {
               Gaming.uiGroup.vehicleUI.gotoSkillOne(da0 as VehicleData);
            }
         }
         else if(label0 == "weaponEdit")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("副手编辑","请输入编辑指令",function(str:String):void{
               Gaming.uiGroup.btnList.WeaponEdit(str);
            },"yesAndNo",999);
         }
         else if(label0 == "shieldEdit")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("护盾编辑","请输入编辑指令",function(str:String):void{
               Gaming.uiGroup.btnList.ShieldEdit(str);
            },"yesAndNo",999);
         }
         else if(label0 == "deviceEdit")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("装置编辑","请输入编辑指令",function(str:String):void{
               Gaming.uiGroup.btnList.DeviceEdit(str);
            },"yesAndNo",999);
         }
         else if(label0 == "jewelryEdit")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("饰品编辑","请输入编辑指令",function(str:String):void{
               Gaming.uiGroup.btnList.JewelryEdit(str);
            },"yesAndNo",999);
         }
         else if(label0 == "vehicleEdit")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("载具编辑","请输入编辑指令",function(str:String):void{
               Gaming.uiGroup.btnList.VehicleEdit(str);
            },"yesAndNo",999);
         }
         else if(label0 == "equipEdit")
         {
            // 显示分页装备编辑菜单
            var menuText:String = "装备编辑菜单\n\n";
            menuText += "【1】基础属性编辑\n";
            menuText += "【2】技能管理编辑\n";
            menuText += "【3】时间设置编辑\n\n";
            menuText += "请输入页面编号(1-3)或直接输入指令：";

            Gaming.uiGroup.alertBox.textInput.showTextInput("装备编辑", menuText, function(str:String):void{
               ItemsGripBtnListCtrl.handleEquipEditMenu(str);
            },"yesAndNo",999);
         }
      }

      // 处理装备编辑菜单选择
      public static function handleEquipEditMenu(selection:String) : void
      {
         var pageNum:int = parseInt(selection);

         switch(pageNum)
         {
            case 1:
               ItemsGripBtnListCtrl.showBasicEditPage();
               break;
            case 2:
               ItemsGripBtnListCtrl.showSkillEditPage();
               break;
            case 3:
               ItemsGripBtnListCtrl.showTimeEditPage();
               break;
            default:
               // 直接执行指令
               Gaming.uiGroup.btnList.EquipEdit(selection);
               break;
         }
      }

      // 页面1：基础属性编辑
      private static function showBasicEditPage() : void
      {
         var helpText:String = "基础属性编辑\n\n";
         helpText += "00*数值 或 等级*数值      # 设置装备等级\n";
         helpText += "01*颜色 或 颜色*颜色      # 设置装备品质\n";
         helpText += "02*数值 或 强化*数值      # 设置强化等级\n";
         helpText += "03*数值 或 进化*数值      # 设置进化等级\n";
         helpText += "04*类型 或 类型*类型      # 设置装备类型\n";
         helpText += "05*数值 或 基础等级*数值   # 设置基础等级\n\n";
         helpText += "颜色: 白绿蓝紫橙红黑暗金紫金氩星\n";
         helpText += "类型: 衣服裤子头盔腰带时装\n\n";
         helpText += "示例: 00*99&01*红&02*20&03*5\n\n";
         helpText += "请输入编辑指令：";

         Gaming.uiGroup.alertBox.textInput.showTextInput("基础属性编辑", helpText, function(str:String):void{
            Gaming.uiGroup.btnList.EquipEdit(str);
         },"yesAndNo",999);
      }

      // 页面2：技能管理编辑
      private static function showSkillEditPage() : void
      {
         var helpText:String = "技能管理编辑\n\n";
         helpText += "09*技能 或 技能添加*技能   # 添加单个技能\n";
         helpText += "09*全部 或 技能添加*全部   # 添加所有技能\n";
         helpText += "10*技能 或 技能删除*技能   # 删除技能\n\n";
         helpText += "常用技能代码:\n";
         helpText += "头盔/腰带: godHand_equip immune_equip\n";
         helpText += "战衣/裤子: sacrifice_equip backStrong_equip\n";
         helpText += "时装: summonWolf_bigBoss zoomOut\n\n";
         helpText += "示例: 09*全部 或 09*godHand_equip\n\n";
         helpText += "请输入编辑指令：";

         Gaming.uiGroup.alertBox.textInput.showTextInput("技能管理编辑", helpText, function(str:String):void{
            Gaming.uiGroup.btnList.EquipEdit(str);
         },"yesAndNo",999);
      }

      // 页面3：时间设置编辑
      private static function showTimeEditPage() : void
      {
         var helpText:String = "时间设置编辑\n\n";
         helpText += "06*时间 或 获取时间*时间   # 设置获取时间\n";
         helpText += "07*时间 或 到期时间*时间   # 设置到期时间\n";
         helpText += "08 或 永不过期           # 设置永不过期\n";
         helpText += "11 或 copyData          # 清除复制数据\n\n";
         helpText += "时间格式: 2025-05-20 12:00:00\n\n";
         helpText += "示例: 08 或 07*2025-12-31 23:59:59\n\n";
         helpText += "请输入编辑指令：";

         Gaming.uiGroup.alertBox.textInput.showTextInput("时间设置编辑", helpText, function(str:String):void{
            Gaming.uiGroup.btnList.EquipEdit(str);
         },"yesAndNo",999);
      }

      private static function thingsTip(da0:ThingsData, dg0:ThingsDataGroup) : Array
      {
         var listArr0:Array = [];
         var d0:ThingsDefine = da0.save.getDefine();
         var arr2:Array = d0.getBtnList();
         if(Boolean(arr2))
         {
            listArr0 = arr2;
         }
         else
         {
            if(d0.canSellB)
            {
               listArr0.push("sell");
            }
            if(d0.isPropsB())
            {
               listArr0.push("use");
               if(da0.save.nowNum > 1)
               {
                  listArr0.push("useAll");
               }
            }
         }
         if(da0.canRefiningB())
         {
            listArr0.push("refining");
         }
         if(d0.canAutoUseB())
         {
            listArr0.push(da0.save.autoUseB ? "noAutoUse" : "autoUse");
         }
         return listArr0;
      }
      
      private static function thingsClick(label0:String) : void
      {
         var da0:ThingsData = nowDa as ThingsData;
         var dg0:ThingsDataGroup = nowDg as ThingsDataGroup;
         var d0:ThingsDefine = da0.save.getDefine();
         if(label0 == "sell")
         {
            sell(da0,dg0);
         }
         else if(label0 == "destroy")
         {
            destroy(da0,dg0);
         }
         else if(label0 == "use")
         {
            ThingsUseCtrl.useThings(dg0,da0.save.name,1,true);
         }
         else if(label0 == "useAll")
         {
            useThingsAll();
         }
         else if(label0 == "autoUse")
         {
            da0.save.autoUseB = true;
            fleshAllBy(dg0,false);
         }
         else if(label0 == "noAutoUse")
         {
            da0.save.autoUseB = false;
            fleshAllBy(dg0,false);
         }
         else if(label0 == "compose")
         {
            if(d0.isNormalChipB())
            {
               ItemsComposeCtrl.compose(da0);
            }
            else if(d0.haveChipTipB())
            {
               BlackEquipComposeCtrl.composeAll(da0);
            }
         }
         else if(label0 == "composeNum")
         {
            if(d0.haveChipTipB())
            {
               BlackEquipComposeCtrl.composeNumClick(da0);
            }
         }
         else if(label0 == "conver")
         {
            Gaming.uiGroup.forgingUI.gotoByOneData(da0,"blackChipConver");
         }
         else if(label0 == "refining")
         {
            if(d0.isPartsChestB())
            {
               ItemsRefiningCtrl.refiningParstChest(da0,dg0);
            }
            else
            {
               ItemsRefiningCtrl.refiningChip(da0,dg0);
            }
         }
      }
      
      private static function useThingsAll() : void
      {
         var num0:Number = Number(nowDa.getNowNum());
         var max0:int = ThingsUseCtrl.getUseMax();
         if(num0 > max0)
         {
            num0 = max0;
         }
         Gaming.uiGroup.alertBox.showNumChoose("请选择使用数量：",num0,num0,1,1,useThingsAllFun);
      }
      
      private static function useThingsAllFun(num0:int) : void
      {
         ThingsUseCtrl.useThings(nowDg as ThingsDataGroup,nowDa.getSave().name,num0,true);
      }
      
      private static function partsTip(da0:ThingsData, dg0:PartsDataGroup) : Array
      {
         var surportB0:Boolean = false;
         var listArr0:Array = [];
         var d0:ThingsDefine = da0.save.getDefine();
         var armsDa0:ArmsData = Gaming.uiGroup.partsUI.assemblyBox.nowArmsData;
         var assemblyB:Boolean = Boolean(armsDa0) && Gaming.uiGroup.partsUI.getNowTrueLabel() == "assembly";
         var canB:Boolean = PartsMethod.canB();
         if(dg0.placeType == ItemsDataGroup.PLACE_BAG)
         {
            if(canB)
            {
               listArr0 = ["compose"];
               if(Boolean(armsDa0) && assemblyB)
               {
                  surportB0 = PartsType.supportType(armsDa0,d0.objType);
                  if(surportB0)
                  {
                  }
                  listArr0 = ["load"].concat(listArr0);
               }
            }
            if(d0.isPartsRareB() == false)
            {
               listArr0.push("sell");
            }
         }
         else
         {
            listArr0 = ["unload"];
            if(!canB)
            {
               listArr0 = [];
            }
         }
         return listArr0;
      }
      
      private static function partsClick(label0:String) : void
      {
         var da0:ThingsData = nowDa as ThingsData;
         var dg0:PartsDataGroup = nowDg as PartsDataGroup;
         if(label0 == "sell")
         {
            sell(da0,dg0);
         }
         else if(label0 == "compose")
         {
            UIShow.showApp("parts",true);
            Gaming.uiGroup.partsUI.gotoCompose(da0);
         }
         else if(label0 == "load")
         {
            PartsMethod.load(da0,dg0);
         }
         else if(label0 == "unload")
         {
            PartsMethod.unload(da0,dg0);
         }
      }
      
      private static function skillTip(da0:HeroSkillData, dg0:HeroSkillDataGroup) : Array
      {
         var listArr0:Array = [];
         var d0:HeroSkillDefine = da0.save.getDefine();
         if(dg0.placeType == ItemsDataGroup.PLACE_BAG)
         {
            if(d0.noNeedEquipB)
            {
               if(da0.save.effB)
               {
                  listArr0.push("closeEffect");
               }
               else
               {
                  listArr0.push("openEffect");
               }
            }
         }
         return listArr0;
      }
      
      private static function skillClick(label0:String) : void
      {
         var da0:HeroSkillData = nowDa as HeroSkillData;
         var dg0:HeroSkillDataGroup = nowDg as HeroSkillDataGroup;
         if(label0 == "closeEffect")
         {
            da0.save.effB = false;
            fleshNowGrip();
         }
         else if(label0 == "openEffect")
         {
            da0.save.effB = true;
            fleshNowGrip();
         }
         else if(label0 == "unload")
         {
            unload(da0,dg0);
         }
      }
      
      private static function moreTip(da0:MoreData, dg0:MoreDataGroup) : Array
      {
         var playerString0:String = null;
         var listArr0:Array = [];
         if(dg0.placeType == ItemsDataGroup.PLACE_BAG)
         {
            listArr0 = ["fight"];
         }
         else
         {
            playerString0 = da0.DATA.getPlayerCtrlString();
            if(playerString0 != "p1")
            {
               if(playerString0 == "p2")
               {
                  listArr0.push("defence");
                  listArr0.push("aiCtrl");
               }
               else
               {
                  listArr0.push("defence");
                  listArr0.push("playerCtrl");
               }
            }
         }
         return listArr0;
      }
      
      private static function moreClick(label0:String) : void
      {
         var site2:int = 0;
         var da0:MoreData = nowDa as MoreData;
         var dg0:MoreDataGroup = nowDg as MoreDataGroup;
         var dg2:MoreDataGroup = Gaming.PG.getOpposingDataGroup(dg0) as MoreDataGroup;
         if(label0 == "fight")
         {
            site2 = dg2.spaceSiteOf();
            if(site2 == -1)
            {
               site2 = 0;
            }
            ItemsGripMoveCtrl.swap(dg0,dg2,da0.getSave().site,site2);
         }
         else if(label0 == "defence")
         {
            unload(da0,dg0);
         }
         else if(label0 == "playerCtrl")
         {
            Gaming.PG.da.moreWay.setMoreDataPlayerCtrl(da0,true);
         }
         else if(label0 == "aiCtrl")
         {
            Gaming.PG.da.moreWay.setMoreDataPlayerCtrl(da0,false);
         }
         Gaming.uiGroup.moreBox.fleshData();
      }
      
      private static function geneTip(da0:GeneData, dg0:GeneDataGroup) : Array
      {
         var lockStr0:String = da0.save.getLockB() ? "unlock" : "lock";
         var isGamingB:Boolean = Gaming.LG.isGaming();
         var listArr0:Array = [];
         if(da0.placeType == "")
         {
            return listArr0;
         }
         listArr0 = [lockStr0,"decomposeDrug","sell"];
         if(!isGamingB)
         {
            listArr0.push("breed");
         }
         return listArr0;
      }
      
      private static function geneClick(label0:String) : void
      {
         var da0:GeneData = nowDa as GeneData;
         var dg0:GeneDataGroup = nowDg as GeneDataGroup;
         if(label0 == "breed")
         {
            if(Gaming.PG.da.pet.getSpaceNum() > 0)
            {
               dg0.removeData(da0);
               Gaming.PG.da.pet.addByGeneData(da0);
               Gaming.uiGroup.alertBox.showSuccess("你已成功把该基因体培育成了尸宠！\n请前往“尸宠”界面查看。");
               fleshAllBy(dg0);
            }
            else
            {
               Gaming.uiGroup.alertBox.showError("尸宠背包空位不足，无法培养！\n你可以放生多余的尸宠，以腾出空位。");
            }
         }
         else if(label0 == "decomposeDrug")
         {
            GeneDataDecompose.decomposeOne(da0,dg0);
         }
         else if(label0 == "unlock")
         {
            unlock(da0,dg0);
         }
         else if(label0 == "lock")
         {
            lock(da0,dg0);
         }
         else if(label0 == "sell")
         {
            sell(da0,dg0);
         }
      }
      
      private static function destroy(da0:IO_ItemsData, dg0:ItemsDataGroup) : void
      {
         nowDa = da0;
         nowDg = dg0;
         var str0:String = "你确定要摧毁" + da0.getCnType() + " " + ComMethod.color(da0.getSave().cnName,"#00FF00") + " ？";
         Gaming.uiGroup.alertBox.showCheck(str0,"yesAndNo",0,check_destroy);
      }
      
      private static function check_destroy() : void
      {
         var str0:String = "再次提示！你确定要摧毁" + nowDa.getCnType() + " " + ComMethod.color(nowDa.getSave().cnName,"#00FF00") + " ？";
         Gaming.uiGroup.alertBox.showCheck(str0,"yesAndNo",0,yes_destroy);
      }
      
      private static function yes_destroy() : void
      {
         var da0:IO_ItemsData = nowDa;
         var dg0:ItemsDataGroup = nowDg;
         dg0.removeData(da0);
         Gaming.TG.dat.remove(da0,dg0);
         Gaming.soundGroup.playSound("uiSound","changeLabel");
         fleshAllBy(dg0);
      }
      
      private static function decomposeItemsData(da0:IO_ItemsData, dg0:ItemsDataGroup) : void
      {
         var de_saveArr0:Array = null;
         var de_tipStr0:String = null;
         if(da0.getSave().getLockB())
         {
            Gaming.uiGroup.alertBox.showError("已锁定物品不能被拆解。");
         }
         else
         {
            nowDa = da0;
            nowDg = dg0;
            de_saveArr0 = PartsCreator.getDecomposeArr(da0,true);
            de_tipStr0 = "拆解该物品能够获得以下零件，是否继续拆解？\n";
            de_tipStr0 += PartsMethod.getAlertTextBySaveArr(de_saveArr0);
            Gaming.uiGroup.alertBox.showNormal(de_tipStr0,"yesAndNo",yes_decomposeItemsDataPan);
         }
      }
      
      private static function yes_decomposeItemsDataPan() : void
      {
         importantCheck(nowDa,"拆解",yes_decomposeItemsData);
      }
      
      private static function yes_decomposeItemsData() : void
      {
         var da0:IO_ItemsData = nowDa;
         var dg0:ItemsDataGroup = nowDg;
         var partsBag0:PartsDataGroup = Gaming.PG.da.partsBag;
         var ct0:CheckText = PartsMethod.decompose(da0,dg0,partsBag0);
         if(ct0.state == "")
         {
            fleshAllBy(dg0,false);
            Gaming.uiGroup.alertBox.showCheck("拆解成功！请到零件背包查看。","",0.25,null,null,"yes","icon");
         }
         else
         {
            Gaming.uiGroup.alertBox.showError(ct0.tipStr0);
         }
      }
      
      private static function sell(da0:IO_ItemsData, dg0:ItemsDataGroup) : void
      {
         nowDa = da0;
         nowDg = dg0;
         var price0:Number = Number(da0.getSellPrice());
         var converNum0:int = int(da0.getAllUpgradeConverStoneNum());
         var str0:String = "你确定要出售" + da0.getCnType() + " " + ComMethod.color(da0.getSave().cnName,"#00FF00") + " ？\n出售后可获得：";
         str0 += "\n银币：" + ComMethod.color(price0 + "","#FFFF00");
         if(converNum0 > 0)
         {
            str0 += "    转化石：" + ComMethod.color(converNum0 + "","#FFFF00");
         }
         Gaming.uiGroup.alertBox.showCheck(str0,"yesAndNo",0,yes_sellPan);
      }
      
      private static function yes_sellPan() : void
      {
         importantCheck(nowDa,"出售",yes_sell);
      }
      
      private static function yes_sell() : void
      {
         var da0:IO_ItemsData = nowDa;
         var dg0:ItemsDataGroup = nowDg;
         var price0:Number = Number(da0.getSellPrice());
         dg0.removeData(da0);
         Gaming.PG.da.main.addCoin(price0);
         var converNum0:int = int(da0.getAllUpgradeConverStoneNum());
         if(converNum0 > 0)
         {
            Gaming.PG.da.thingsBag.addDataByName("converStone",converNum0);
         }
         flesh_affter_sell(da0,dg0);
      }
      
      private static function flesh_affter_sell(da0:IO_ItemsData, dg0:ItemsDataGroup) : void
      {
         Gaming.TG.dat.remove(da0,dg0);
         Gaming.soundGroup.playSound("uiSound","sell");
         fleshAllBy(dg0);
         UIShow.flesh_coinChange();
      }
      
      public static function batchSell(arr0:Array, dg0:ItemsDataGroup) : void
      {
         var n:* = undefined;
         var da0:IO_ItemsData = null;
         var cNum0:int = 0;
         var price0:Number = 0;
         var converNum0:int = 0;
         for(n in arr0)
         {
            da0 = arr0[n];
            price0 += da0.getSellPrice();
            cNum0 = int(da0.getAllUpgradeConverStoneNum());
            converNum0 += cNum0;
         }
         dg0.removeDataArr(arr0);
         Gaming.TG.dat.removeArr(arr0,dg0);
         if(converNum0 > 0)
         {
            Gaming.PG.da.thingsBag.addDataByName("converStone",converNum0);
         }
         Gaming.PG.da.main.addCoin(price0);
         Gaming.soundGroup.playSound("uiSound","sell");
         Gaming.soundGroup.playSound("uiSound","sell");
         UIShow.flesh_coinChange();
      }
      
      private static function unload(da0:IO_ItemsData, dg0:ItemsDataGroup) : void
      {
         var str0:String = null;
         var dg2:ItemsDataGroup = Gaming.PG.getOpposingDataGroup(dg0);
         var index0:int = dg2.spaceSiteOf();
         if(index0 == -1)
         {
            str0 = "背包找不到空位，无法卸下！";
            Gaming.uiGroup.alertBox.showCheck(str0,"yes",0,null,null,"no","icon");
         }
         else
         {
            ItemsGripMoveCtrl.swap(dg0,dg2,da0.getSave().site,index0);
         }
         fleshAllBy(dg0);
      }
      
      private static function unloadTo(da0:IO_ItemsData, dg0:ItemsDataGroup, targetDg0:ItemsDataGroup) : void
      {
         var str0:String = null;
         var index0:int = targetDg0.spaceSiteOf();
         if(index0 == -1)
         {
            str0 = targetDg0.getPlaceCnName() + "找不到空位，无法放入！";
            Gaming.uiGroup.alertBox.showError(str0);
         }
         else
         {
            ItemsGripMoveCtrl.swapAndSpcialPan(dg0,targetDg0,da0.getSave().site,index0);
            fleshAllBy(dg0);
         }
      }
      
      private static function lock(da0:IO_ItemsData, dg0:ItemsDataGroup) : void
      {
         da0.getSave().setLockB(true);
         fleshAllBy(dg0,false);
      }
      
      private static function unlock(da0:IO_ItemsData, dg0:ItemsDataGroup) : void
      {
         da0.getSave().setLockB(false);
         fleshAllBy(dg0,false);
      }
   }
}

