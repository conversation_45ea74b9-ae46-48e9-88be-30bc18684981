# 装备编辑基础版指南

## 🎯 功能说明

装备编辑功能已恢复为最基础的版本，只保留核心的装备属性编辑功能，删除了所有复杂的扩展属性和分页界面。

## 🔧 基础指令

### 装备基础属性编辑

| 指令代码 | 中文名称 | 说明 | 示例 |
|---------|---------|------|------|
| 00*数值 | 等级*数值 | 设置装备等级 | 00*99 |
| 01*颜色 | 颜色*颜色 | 设置装备品质 | 01*红 |
| 02*数值 | 强化*数值 | 设置强化等级 | 02*20 |
| 03*数值 | 进化*数值 | 设置进化等级 | 03*5 |
| 04*类型 | 类型*类型 | 设置装备类型 | 04*衣服 |
| 05*数值 | 基础等级*数值 | 设置基础等级 | 05*50 |
| 06*时间 | 获取时间*时间 | 设置获取时间 | 06*2025-05-20 12:00:00 |
| 07*时间 | 到期时间*时间 | 设置到期时间 | 07*2025-12-31 23:59:59 |
| 08 | 永不过期 | 设置永不过期 | 08 |
| 09*技能 | 技能添加*技能 | 添加技能 | 09*godHand_equip |
| 10*技能 | 技能删除*技能 | 删除技能 | 10*godHand_equip |
| 11 | copyData | 清除复制数据 | 11 |

## 🎮 使用方法

### 方法1：右键装备编辑
1. 在背包中右键点击装备
2. 选择"装备编辑"
3. 输入编辑指令
4. 确认执行

### 方法2：装备重铸界面
1. 进入锻造界面 → 装备 → 装备重铸
2. 选择要编辑的装备
3. 点击"编辑当前数据"
4. 输入编辑指令

## 📋 详细说明

### 装备品质选项
- 白、绿、蓝、紫、橙、红、黑、暗金、紫金、氩星

### 装备类型选项
- 衣服、裤子、头盔、腰带、时装

### 时间格式
- 标准格式：2025-05-20 12:00:00
- 年-月-日 时:分:秒

### 技能代码示例

#### 头盔/腰带技能
- godHand_equip - 上帝的护佑
- immune_equip - 免疫
- magneticField_equip - 磁力场
- strongHalo_equip - 顽强光环
- murderous_equip - 嗜爪之怒
- poisonRange_equip - 瘴气

#### 战衣/裤子技能
- sacrifice_equip - 牺牲
- backStrong_equip - 背水一战
- anionSkin_equip - 负离子皮肤
- bloodSuck_equip - 吸血
- fireWall_equip - 火墙
- iceWall_equip - 冰墙

#### 时装技能
- summonWolf_bigBoss - 召唤狼王
- zoomOut - 缩小
- invisible - 隐身
- speedUp - 加速

## 🧪 使用示例

### 基础装备设置
```
00*99&01*红&02*20&03*5
```
设置：99级、红色品质、强化20级、进化5级

### 完整装备设置
```
00*99&01*氩星&02*99&03*9&04*衣服&05*99&08&09*godHand_equip
```
设置：99级、氩星品质、强化99级、进化9级、衣服类型、基础99级、永不过期、添加上帝护佑技能

### 技能管理
```
09*godHand_equip&09*immune_equip
```
添加上帝护佑和免疫技能

```
10*godHand_equip
```
删除上帝护佑技能

### 时间设置
```
06*2025-01-01 00:00:00&07*2025-12-31 23:59:59
```
设置获取时间和到期时间

```
08
```
设置永不过期

## 🔍 注意事项

1. **指令格式**：使用 `*` 分隔指令代码和数值
2. **多指令**：使用 `&` 连接多个指令
3. **大小写**：颜色和类型名称不区分大小写
4. **技能代码**：必须使用正确的技能代码
5. **时间格式**：必须严格按照格式输入

## 🚀 快速测试

### 简单测试
```
00*50
```
设置装备等级为50级

### 组合测试
```
00*99&01*红&02*10
```
设置99级红色装备，强化10级

### 完整测试
```
00*99&01*氩星&02*99&03*9&08&09*godHand_equip
```
创建一个完整的神级装备

## 📝 总结

基础版装备编辑功能专注于核心功能：
- ✅ 装备等级、品质、强化、进化设置
- ✅ 装备类型和基础等级设置
- ✅ 获取时间和到期时间管理
- ✅ 技能添加和删除
- ✅ 复制数据清除

这个版本简洁高效，适合日常装备编辑需求！
